# BDG-PRO

A Next.js project for Bris de Glace Pro, a windshield replacement service.

## Features

- Responsive design with mobile and desktop layouts
- Dark/light theme support
- Animated sections with testimonials, features, and partner logos
- Multiple sections including a hero section, features, testimonials, and more

## Technologies Used

- Next.js 15
- React 19
- TypeScript
- Tailwind CSS
- Framer Motion for animations
